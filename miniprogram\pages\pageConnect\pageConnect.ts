import { IAppOption } from "../../../typings/index"
import { BluetoothEventCallback, BluetoothManager } from "../../lib/bluetoothManager"

// pages/pageConnect/pageConnect.ts
const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager
var sBluetoothEventCallback: BluetoothEventCallback

Page({

  /**
   * 页面的初始数据
   */
  data: {
    hiddenmodalput: true,
    triggerde: true,
    Isfreshing: false,
    connectedDevice: <any>null,
    deviceList: <any>[],
    isRefresh: true,
    nameText: "SR20-", //蓝牙名称过滤。SR20-
    bleText: "",
    catchText: "",
    isShowLoading:false,
    fakeDeviceList: <any>[],
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    app.globalData.connectGoToUrl = "../pageSetting/pageSetting";
    sBluetoothManager = app.globalData.bluetoothManager
  },
  onShow() {
    // console.log('这里', getCurrentPages()[0].route.match(/pageConnect/)[0] === "pageConnect")
    wx.setStorageSync("isReconnect",false)
    wx.setStorageSync("showToast",false)
    sBluetoothEventCallback = new BluetoothEventCallback();
    sBluetoothEventCallback.onFoundDev = this._OnFoundDevs
    sBluetoothEventCallback.onDevStatusSuccess = this._onDevConnectSuccess
    sBluetoothEventCallback.onDevStatusDisconnect = this._onDevDisconnect
    sBluetoothEventCallback.onDevStatusFailed = this._onDevConnectFailed
    sBluetoothManager.addBluetoothEventCallback(sBluetoothEventCallback)
    this._scanDevice()
    this._initDevice();
    // 检测是否开启了蓝牙
    wx.openBluetoothAdapter({
      fail: function (res) {
        wx.showToast({
          title:'请开启蓝牙',
          icon:'none',
          duration:3000
        })
      }
    });
    let nameMap = wx.getStorageSync("nameMap");
    wx.setStorageSync("nameMap",nameMap)
    this.setData({isShowLoading:false})
    setTimeout(() => {
      this.onPullDownRefresh()
    }, 500);
  },
  onUnload() {
    sBluetoothManager.removeBluetoothEventCallback(sBluetoothEventCallback)
  },
  //下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新');
    if (sBluetoothManager.getConnectedDevice()) {
      sBluetoothManager.disconnectDevice()
      console.log('正在选择设备'); //设置 -未连接
    }
    this.setData({
      deviceList:[]
    })
    this.onScrollviewRefresh();
  },

  //==============================后面为自定义的方法 start========================

  onSelectedDevice: function (e: any) {
    console.log(e.target.dataset.index)
    let device = e.currentTarget.dataset.item;
    let connectedDevice = sBluetoothManager.getConnectedDevice();
    if (connectedDevice != null) {
      if (device.deviceId !== connectedDevice.deviceId) {
        wx.showToast({
          title: '请先断开已连接的设备',
          icon: 'none'
        })
        return;
      } else {
        wx.showModal({
          title: '提示',
          content: '是否要断开该设备',
          success(res) {
            if (res.confirm) {
              sBluetoothManager.disconnectDevice()
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
      }
    } else {
      console.log(device);
      if (sBluetoothManager.connectDevice(device, false)) {
        wx.showLoading({
          title: '连接中',mask:true
        })
      } 
    }
    // }
  },
  //下拉滚动条
  onScrollviewRefresh() {
    if (this.data.Isfreshing) return;
    this.setData({
      Isfreshing: true
    })
    if (!this.data.triggerde) {
      this.setData({
        triggerde: true
      })
    }//保证刷新状态下，triggered为true  

    console.log("搜索设备...")
    this._scanDevice()
    if (this.data.isShowLoading) {
      wx.showLoading({
        title: '加载中',mask:true
      })
    }

    setTimeout(() => {
      wx.hideLoading()
      wx.stopPullDownRefresh();
      this.setData({
        triggerde: false,//触发onRestore，关闭刷新图标  
        Isfreshing: false,
        isShowLoading:true,
      })
    }, 1000);
  },

  //点击修改名称
  onShowUpdateName: function (e: any) {
    let name = e.target.dataset.name;
    let localName = e.target.dataset.localName;
    this.setData({
      hiddenmodalput: !this.data.hiddenmodalput,
      bleText: name,
      catchText: localName
    })
  },

  //取消按钮
  onCancel: function () {
    this.setData({
      hiddenmodalput: true
    });
  },
  //确认修改
  onConfirm: function () {
    //格式化蓝牙名称：将name优先显示，为空显示localName，然后使用localName保存每个用户的缓存值。
    let nameMap = wx.getStorageSync("nameMap");
    if (!nameMap) {
      nameMap = {};
    }
    nameMap[this.data.bleText] = this.data.catchText;
    wx.setStorageSync('nameMap', nameMap);              //保存已经配置好的列表。
    this.setData({
      hiddenmodalput: true
    })
    this._initDevice();
  },
  onInput: function (e: any) {
    console.log("蓝牙名称修改内容：" + e.detail.value)
    this.setData({
      catchText: e.detail.value
    })
  },
  _scanDevice() {
    //Android平台检测是否有开启位置权限，
    let info = wx.getSystemInfoSync()
    console.error(info)
    //检测是否有位置权限
    if (info.platform == "android" && !info.locationAuthorized) {
      wx.showToast({
        title: '请授予微信位置权限(GPS)',
        icon: 'none'
      })
      return
    }
    //检测是否打开gps位置开关
    if (info.platform == "android" && !info.locationEnabled) {
      wx.showToast({
        title: '请打开位置信息(GPS)',
        icon: 'none'
      })
      return
    }
    sBluetoothManager.sanDevice();
  },
  _OnFoundDevs(devices: WechatMiniprogram.BlueToothDevice[]) {
    // console.log('一开始：',devices);
    // 做SR20筛选
    // devices.forEach((item,index) =>{
    //   if (item.name.split("SR20")[1] === undefined) {
    //       devices.splice(index,1)
    //   }
    // })


    let devicesTemp: WechatMiniprogram.BlueToothDevice[] = new Array(devices.length + 1);
    let isContainConnected: boolean = false
    let isConnected = sBluetoothManager.isConnected()
    if (isConnected) {
      let connectedDevice = sBluetoothManager.getConnectedDevice()!;
      devicesTemp[0] = connectedDevice;
      for (let index = 0; index < devices.length; index++) {
        devicesTemp[index + 1] = devices[index]
        if (connectedDevice.deviceId == devices[index].deviceId) {
          isContainConnected = true
        }
      }
    }

    var devicesShow = new Array();
    console.log('!isContainConnected && isConnected => ', !isContainConnected && isConnected)
    if (!isContainConnected && isConnected) {
      if (this.data.nameText.length > 0) {
        for (let i = 0; i < devicesTemp.length; i++) {
          let tmp = devicesTemp[i]
          let upName = tmp.localName.toUpperCase()
          let upNameText = this.data.nameText.toUpperCase()
          if (upName.indexOf(upNameText) >= 0) {
            devicesShow.push(tmp)
          }
        }
        // this.setData({
        //   deviceList: devicesShow
        // })
        this.setData({
          fakeDeviceList: devicesShow
        })
      } else {
        this.setData({
          deviceList: devicesTemp
        })
      }
    } else {
      if (this.data.nameText.length > 0) {
        for (let i = 0; i < devices.length; i++) {
          let tmp = devices[i]
          let upName = tmp.localName.toUpperCase()
          let upNameText = this.data.nameText.toUpperCase()
          if (upName.indexOf(upNameText) >= 0) {
            devicesShow.push(tmp)
          }
        }
        // this.setData({
        //   deviceList: devicesShow
        // })
        this.setData({
          fakeDeviceList: devicesShow,
          deviceList: devicesShow
        })
      } else {
        this.setData({
          deviceList: devices
        })
      }
    }
    //1.5秒刷新一次，不然只会显示一台设备。
    // if (this.data.isRefresh) {
    //   this.data.isRefresh = false;
    //   setTimeout(() => {
    //     this.data.isRefresh = true;
    //     this._initDevice();
    //   }, 1500);
    // }
  },
  _initDevice: function () {
    //格式化蓝牙名称：将name优先显示，为空显示localName，然后使用localName保存每个用户的缓存值。
    let nameMap = wx.getStorageSync("nameMap");
    // let deviceArr = this.data.deviceList;
    let deviceArr = this.data.fakeDeviceList;
    if (!nameMap) {
      nameMap = {};
    }
    if (deviceArr) {
      for (var i = 0; i < deviceArr.length; i++) {
        // //name为空时将localName 赋值给name
        if (deviceArr[i] && !deviceArr[i].name && deviceArr[i].localName) {
          deviceArr[i].name = deviceArr[i].localName.toLowerCase();
        }
        //将缓存值赋值给localName
        if (nameMap[deviceArr[i].name]) {
          deviceArr[i].localName = nameMap[deviceArr[i].name];
        } else {
          deviceArr[i].localName = deviceArr[i].name;
        }
      }
      this.setData({
        deviceList: deviceArr
      })

    }
  },
  _onDevDisconnect: function () {
    this.setData({
      connectedDevice: null
    })
  },
  _onDevConnectFailed: function () {
    this.setData({
      connectedDevice: null
    })
    wx.hideLoading({
      success: () => {
        wx.showToast({
          title: '连接失败',
          icon: 'none'
        })
      },
    })
  },
  _onDevConnectSuccess: function (device: WechatMiniprogram.BlueToothDevice,) {
    this.setData({
      connectedDevice: device,
    })
    wx.setStorageSync("showToast",true) 
    app.globalData.device = device;
    app.globalData.store.device = device;
    if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageConnect/) != null) {
      wx.hideLoading({
        success: () => {
          wx.showToast({
            title: '连接成功,即将跳转',
            icon: 'none',
            duration: 1500
          })
          setTimeout(function () {
            wx.navigateTo({
              url: '../pageSetting/pageSetting'
            })
          }, 2500)
        },
      })
    }

  },
})