// pages/pageBL1001/pageBL1001.ts
import { IAppOption } from "../../../typings/index";
import { BleDataCallback, BleDataHandler } from "../../lib/ble-data-handler";
import { BluetoothManager } from "../../lib/bluetoothManager";
import toast from "../LB1001Setting/toast";
import {
  initBLTChange,
  sendData,
  disConnectSoBack,
  hexToDec,
  decimalToHexadecimal,
  reConnection,
  ab2hex,
  asciiToHex,
} from "../LB1001Setting/index";
import AnalyzeData from "../LB1001Setting/AnalyzeData";
import { getForgetAPI } from "../../utils/http";

const app = getApp<IAppOption>();
var sBluetoothManager: BluetoothManager;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    openValue: "开门",
    silderValue1: 1,
    silderValue2: 2,
    checked: false,
    lbstatus: 0, //0代表关门 1代表开门
    lblong: 0,
    isStudy: true, //是否在学习模式，如果不在，设备也不回复则判定为掉线
    isShowToast: false, //是否显示弹窗
    toastSuccess: true, //弹窗的类型 //1为蓝，0为红
    toastText: "操作成功", //弹窗文字
    arrlist: [] as number[],
    myarrlist: [] as number[],
    types: false,
    mytypes: false,
    timp: true,
    isShowPwd: false, //设置密码弹窗
    // isHead: true, //弹窗头部
    // isInput: true, //弹窗密码
    isValidation: false, //校验密码弹窗
    inputValue: "",
    passWord: "HiLink", //HiLink
    isFastPwd: true, //第一次进来校验密码
    Version: "1.0",
    showBubble: false, //展示气泡弹窗
    openDoor_left: "left: -24.5%;",
    openDoor_right: "left: 24.5%;",
    distanceCM: 0, //实时距离 距离误差会在±35CM
    max15cm: 0, //15 档位下的实时距离
    isForget: false, //忘记密码弹窗
    forgetVal: "",
    isNo: false,
    timer: null as any, // 定时器
    flag: true,
    handle4F4ECount: 0, // 添加计数器
    maxHandle4F4ECount: 6, // 最大执行次数
    // 新增：区分指令来源与初始化控制
    commandSource: "idle" as "idle" | "user" | "CMing" | "init" | "exiting", // 当前指令来源
    hasInitDistance: false, // 是否已用设备上报初始化过用户感应距离
    isPageExiting: false, // 页面是否正在退出
    lastDistanceCheckTime: 0, // 最后一次距离检查的时间戳
    userOperationPending: false, // 用户操作是否进行中
    userTargetGear: 0, // 用户目标档位（用于验证）
    userOperationTimestamp: 0, // 用户操作时间戳
    bottomCm: 128,
  },
  //红点动画
  getBottomCm() {
    const maxDistance = 0.7 * 15; // 10.5米
    const minBottomCm = 2; // 最远距离时的位置（靠近外圈）
    const maxBottomCm = 128; // 最近距离时的位置（靠近中心）
    let distance = this.data.distanceCM;
    distance = Math.min(distance, maxDistance);
    // 反向线性映射：距离越大，bottomCm越小
    // distance = 0 时，bottomCm = 128（靠近中心）
    // distance = 10.5 时，bottomCm = 2（靠近外圈）
    let bottomCm =
      maxBottomCm - (distance / maxDistance) * (maxBottomCm - minBottomCm);
    // console.log(`距离: ${distance}m, bottomCm: ${bottomCm}px`);
    this.setData({
      bottomCm,
    });
  },
  getForget() {
    let obj = {
      openid: "openid",
      pass: 123456,
    };
    getForgetAPI(obj)
      .then((res) => {
        console.log("忘记密码 -->", res);
      })
      .catch((err) => {
        console.log("忘记密码失败 -->", err);
      });
  },
  //忘记密码
  onForgetPwd(e: any) {
    let isNo = e.currentTarget.dataset.type == 1 ? true : false;
    this.setData({
      isForget: true,
      isValidation: false,
      isNo,
    });
  },
  closeForget() {
    //不能不走校验
    if (this.data.isNo) {
      wx.navigateBack({ delta: 1 });
    }
    this.setData({
      isForget: false,
      forgetVal: "",
    });
  },
  async validaForget() {
    let pwd = this.data.forgetVal;
    if (pwd.length != 6) {
      wx.showToast({
        title: "请输入6位数的密码",
        icon: "none",
      });
    } else {
      this.ForgetPwd();
      this.ForgetNewPwd();
    }
  },
  //本地密码校验
  async ForgetPwd() {
    let pwd = wx.getStorageSync("PassWord");
    let password = asciiToHex(pwd);
    let data = "FFCC0C00" + password + "BBAA";
    await sendData(data);
    await this.checkVersion();
    await this.checkOpendown();
    await this.checkjl();
    await this.checkTime();
    await this.checkKaiguan();
  },
  //设置新的密码
  async ForgetNewPwd() {
    let password = asciiToHex(this.data.forgetVal);
    let data = "FFCC0B00" + password + "BBAA"; //FFCC01000CBBAA
    await sendData(data);
    wx.setStorageSync("PassWord", this.data.forgetVal);
    this.setData({
      isForget: false,
    });
  },
  async openAndClose_door(e: any) {
    const status = e.currentTarget.dataset.status;
    if (status === "1") {
      if (this.data.openValue === "开门") {
        toast.info("当前已开门");
        return;
      }
      let data = "FFCC0600BBAA";
      //console.log("手动开门指令:",data);
      await sendData(data);
      this.checkKaiguan();
      this.setData({
        openDoor_left: "left:-24.5%;",
        openDoor_right: "left:24.5%;",
        openValue: "开门",
      });
    } else {
      if (this.data.openValue === "关门") {
        toast.info("当前已关门");
        return;
      }
      let data = "FFCC0E00BBAA";
      await sendData(data);
      this.checkKaiguan();
      this.setData({
        openDoor_left: "left:0;",
        openDoor_right: "left:0;",
        openValue: "关门",
      });
    }
  },
  handleWenHao(e: any) {
    const showBubble = !this.data.showBubble;
    this.setData({
      showBubble,
    });
  },
  onInput(e: any) {
    this.setData({
      inputValue: e.detail.value,
    });
  },
  onInputow(e: any) {
    this.setData({
      passWord: e.detail.value,
    });
  },
  onInputft(e: any) {
    this.setData({
      forgetVal: e.detail.value,
    });
  },
  /**打开提示窗 */
  openToast(type = 1, text = "操作成功") {
    if (type == 1) {
      this.setData({
        toastText: text,
        toastSuccess: true,
        isShowToast: true,
      });
      setTimeout(() => {
        this.setData({
          isShowToast: false,
        });
      }, 2000);
    } else {
      this.setData({
        toastText: text,
        toastSuccess: false,
        isShowToast: true,
      });
    }
  },
  async Adjustjl() {
    // 暂停实时监听，避免指令冲突
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }
    this.setData({
      types: true,
      arrlist: [],
    });
    if (this.data.types) {
      wx.showLoading({ title: "智能调距中..", mask: true });
      setTimeout(() => {
        this.setData({
          types: false,
        });
        console.log("距离数组：", this.data.arrlist);
        if (this.data.arrlist.length > 0) {
          this.bindMain();
          wx.hideLoading();
          // toast.info("设置成功");
          this.checkjl();
          this.setData({
            arrlist: [],
          });
        } else {
          wx.hideLoading();
          // toast.info("设置成功");
          toast.info("设置失败");
          this.setData({
            arrlist: [],
          });
        }
        this.startTimer(); // 重新启动定时器
      }, 5000);
    }
  },
  calculateAverageWithoutMinMax(arr: number[]) {
    // 过滤掉NaN和无效值
    const validArr = arr.filter((value) => !isNaN(value) && isFinite(value));
    if (validArr.length === 0) {
      console.warn("数组中没有有效数据，无法计算平均值");
      return 0; // 返回默认值
    }

    // 检查数组长度，至少需要3个元素才能去掉最大最小值
    if (validArr.length < 3) {
      // 如果数组长度小于3，直接计算平均值
      let sum = validArr.reduce((a, b) => a + b, 0);
      let average = sum / validArr.length;
      // console.log("平均值：", average);
      let cmNumber = Number((average / 100).toFixed(2));
      console.log("777", cmNumber);
      this.setData({
        distanceCM: cmNumber,
      });
      this.getBottomCm();
      return average;
    }

    // 对数组进行排序
    validArr.sort((a, b) => a - b);
    // 去掉一个最大值和一个最小值
    let newArr = validArr.slice(1, validArr.length - 1);
    // 计算剩余元素的总和
    let sum = newArr.reduce((a, b) => a + b, 0);
    // 计算平均值
    let average = sum / newArr.length;
    // console.log("平均值：", average);
    let cmNumber = Number((average / 100).toFixed(2));
    console.log("777", cmNumber);
    this.setData({
      distanceCM: cmNumber,
    });
    return average;
  },
  /**关闭提示窗 */
  async closeToast() {
    this.setData({ isShowToast: false });
    return true;
  },
  /**关闭提示窗 */
  async closePwd() {
    wx.setStorageSync("isFastPwd", false);
    this.setData({ isShowPwd: false });
    return true;
  },
  // 修改密码
  async editPwd() {
    // if (this.data.isHead) {
    //   this.setData({ isHead: false, isInput: false });
    // } else {
    let pwd = this.data.inputValue;
    if (pwd.length != 6) {
      wx.showToast({
        title: "请输入6位数的密码",
        icon: "none",
      });
    } else {
      let password = asciiToHex(this.data.inputValue);
      let data = "FFCC0B00" + password + "BBAA"; //FFCC01000CBBAA
      await sendData(data);
      wx.setStorageSync("PassWord", this.data.inputValue);
      this.setData({ isShowPwd: false });
    }
    // }
    wx.setStorageSync("isFastPwd", false);
  },
  // 取消校验密码
  async closeVld() {
    wx.navigateBack({ delta: 1 });
  },
  // 校验密码
  async validaPwd() {
    let password = asciiToHex(this.data.passWord);
    let data = "FFCC0C00" + password + "BBAA";
    await sendData(data);
    wx.setStorageSync("PassWord", this.data.passWord);
    await this.checkVersion();
    await this.checkOpendown();
    await this.checkjl();
    await this.checkTime();
    await this.checkKaiguan();
  },
  async bindMain() {
    var num = this.calculateAverageWithoutMinMax(this.data.arrlist);
    let result = Math.floor(num / 70);

    // 确保结果在有效范围内
    if (result <= 0) result = 1;
    if (result > 15) result = 15;

    const currentTimestamp = Date.now();

    // 智能调距时设置明确的用户操作标识
    this.setData({
      commandSource: "user",
      hasInitDistance: false,
      lastDistanceCheckTime: 0, // 清除距离监听时间戳
      userOperationPending: true, // 标记用户操作进行中
      userTargetGear: result, // 记录智能调距的目标档位
      userOperationTimestamp: currentTimestamp,
    });

    if (result <= 0) {
      let data = "FFCC010001BBAA";
      await sendData(data);
    } else if (result > 15) {
      let results = decimalToHexadecimal(15);
      let data = "FFCC0100" + results + "BBAA";
      await sendData(data);
    } else {
      let results = decimalToHexadecimal(result);
      let data = "FFCC0100" + results + "BBAA";
      await sendData(data);
    }
  },

  /** 滑动-感应范围档位 */
  sliderchanging: function (e: any) {
    //滑块的取值
    let value: number = e.detail.value;
    if (value > 15) {
      value = 15;
    }
    if (value === 0) {
      this.setData({
        silderValue1: 1,
      });
    } else {
      this.setData({
        silderValue1: value,
      });
    }
  },

  /**设置-感应范围 */
  async setInduction(_distance = 0) {
    const currentTimestamp = Date.now();

    // 用户手动设置档位时，清除距离监听状态和时间戳
    this.setData({
      commandSource: "user",
      lastDistanceCheckTime: 0, // 清除距离监听时间戳
      mytypes: false, // 停止可能正在进行的距离监听
      userOperationPending: true, // 标记用户操作进行中
      userTargetGear: _distance, // 记录用户目标档位
      userOperationTimestamp: currentTimestamp, // 记录操作时间戳
    });

    // 设置超时保护，5秒后自动清除用户操作标记
    setTimeout(() => {
      if (
        this.data.userOperationPending &&
        this.data.userOperationTimestamp === currentTimestamp
      ) {
        this.setData({
          userOperationPending: false,
          commandSource: "idle",
          userTargetGear: 0,
          userOperationTimestamp: 0,
        });
      }
    }, 5000);

    let distance = decimalToHexadecimal(_distance);
    let data = "FFCC0100" + distance + "BBAA"; //FFCC01000CBBAA
    //console.log("感应范围指令:",data);
    await sendData(data);
  },

  /** 设置-感应范围档位 */
  bindchange: function (e: any) {
    let value: number = e.detail.value < 1 ? 1 : e.detail.value; //最小只能为1
    this.setData({
      silderValue1: value,
    });
    this.setInduction(value);
  },

  /** 滑动-检测无人关门时间 */
  sliderchanging2: function (e: any) {
    //滑块的取值
    let value: number = e.detail.value;
    if (value === 0) {
      this.setData({
        silderValue2: 1,
      });
    } else {
      this.setData({
        silderValue2: value,
      });
    }
  },
  /**设置-无人关门时间 */
  async setNobodyKeeptime(_time = 0) {
    const currentTimestamp = Date.now();

    // 设置用户操作标记，确保能收到成功提示
    this.setData({
      commandSource: "user",
      userOperationPending: true,
      userOperationTimestamp: currentTimestamp,
    });

    // 设置超时保护，5秒后自动清除用户操作标记
    setTimeout(() => {
      if (
        this.data.userOperationPending &&
        this.data.userOperationTimestamp === currentTimestamp
      ) {
        this.setData({
          userOperationPending: false,
          commandSource: "idle",
          userOperationTimestamp: 0,
        });
      }
    }, 5000);

    let time = decimalToHexadecimal(_time);
    let data = "FFCC0200" + time + "BBAA";
    //console.log("无人关门指令:",data);
    await sendData(data);
  },
  /** 设置-检测无人关门时间 */
  bindchange2: function (e: any) {
    let value: number = e.detail.value < 1 ? 1 : e.detail.value; //最小只能为1
    // console.log(value);
    this.setData({
      silderValue2: value,
    });
    this.setNobodyKeeptime(value);
  },

  /**设置-自动开门按钮 */
  async setOpenDoor(_switch = 0) {
    const currentTimestamp = Date.now();

    // 设置用户操作标记，确保能收到成功提示
    this.setData({
      commandSource: "user",
      userOperationPending: true,
      userOperationTimestamp: currentTimestamp,
    });


    let switchs = decimalToHexadecimal(_switch);
    let data = "FFCC0900" + switchs + "BBAA";
    await sendData(data);

    // 延迟跳转，确保能收到设备响应和提示
    setTimeout(() => {
      wx.redirectTo({ url: "/pages/pageLB1001hand/pageLB1001hand" });
    }, 1000);
  },

  // 切换按钮
  changeChecked(e: any) {
    const checked = e.detail.value;
    // console.log(checked);
    this.setData({
      checked,
    });
    if (checked) {
      this.setOpenDoor(1);
    } else {
      this.setOpenDoor(0);
    }
  },

  async connectOpen() {
    let data = "";
    if (this.data.openValue == "开门") {
      data = "FFCC0E00BBAA"; //关门
    } else {
      data = "FFCC0600BBAA"; //开门
      this.setData({ openValue: "开门" });
    }
    //console.log("手动开门指令:",data);
    await sendData(data);
    this.checkKaiguan();
  },

  /** 查看版本 */
  async checkVersion() {
    let data = "FFCC0D00BBAA".replace(/\s/g, "");
    await sendData(data);
  },

  /** 查看当前开关门状态 */
  async checkOpendown() {
    let data = "FFCC0F00BBAA".replace(/\s/g, "");
    await sendData(data);
  },

  /** 查看感觉距离 */
  async checkjl() {
    let data = "FFCC0700BBAA".replace(/\s/g, "");
    await sendData(data);
  },
  /** 查看无人持续时间 */
  async checkTime() {
    let data = "FFCC0800BBAA".replace(/\s/g, "");
    await sendData(data);
  },
  /** 查看是否打开自动开门 */
  async checkKaiguan() {
    let data = "FFCC0A00BBAA".replace(/\s/g, "");
    await sendData(data);
  },
  /** 校验密码 */
  async checkPwd() {
    let pasd = wx.getStorageSync("PassWord");
    if (pasd == null || pasd == "") {
      wx.setStorageSync("PassWord", this.data.passWord);
    }
    let pwd = wx.getStorageSync("PassWord");
    let password = asciiToHex(pwd);
    let data = "FFCC0C00" + password + "BBAA";
    await sendData(data);
    await this.checkVersion();
    await this.checkOpendown();

    // 设置为初始化查询，确保档位能被正确读取
    this.setData({ commandSource: "init" });
    await this.checkjl();

    await this.checkTime();
    await this.checkKaiguan();

    // 在所有初始化查询完成后延迟启动定时器，给用户档位设置一些时间
    setTimeout(() => {
      // this.startTimer();
    }, 1000);
  },
  /*** 是否断开连接了，断开则回连 */
  isConnected() {
    var res = sBluetoothManager.isConnected();
    console.log("设备连接状态", res);
    if (res === false) {
      //重新连接
      toast.info("设备断开连接，正在准备回连");
      this.reConnectionFn(1); //自动重连设备
    }
  },
  /** 执行设备回连逻辑 */
  async reConnectionFn(count: number) {
    if (
      sBluetoothManager.getConnectedDevice()?.deviceId != undefined ||
      getCurrentPages()[getCurrentPages().length - 1].route.match(
        /LB1001Setting/
      ) == null
    ) {
      return;
    }
    console.log("执行次数", count);
    wx.showLoading({ title: "正在连接" + count + "次", mask: true });
    const res: any = await reConnection();
    if (res === true) {
      wx.hideLoading();
      toast.info("设备已重连成功");
      return this.onShow();
    } else if (res === false && count === 1) {
      return await this.reConnectionFn(2);
    } else if (res === false && count === 2) {
      return await this.reConnectionFn(3);
    } else {
      disConnectSoBack("重连失败，请手动连接");
      return;
    }
  },
  toPageSetting() {
    wx.navigateTo({ url: "../LB1001Firmare/LB1001Firmare" });
  },
  //点击返回
  async returnSetp() {
    // 立即设置退出状态，防止在页面切换过程中继续执行距离监听
    this.setData({ isPageExiting: true });

    // 先执行清理操作，确保状态正确
    await this.oFFcm();

    // 然后再导航返回
    wx.navigateBack({ delta: 1 });
  },
  // 修改密码
  updatePwd() {
    this.setData({
      isShowPwd: true,
    });
  },

  /**
   * 处理4F4E有人距离数据的函数
   * 优化频繁处理，添加防抖机制
   */
  async handle4F4EData() {
    console.log(111);
    
    // 检查页面是否正在退出
    if (this.data.isPageExiting || this.data.commandSource === "exiting") {
      console.log("页面正在退出，停止距离监听");
      return;
    }

    // 检查定时器是否还存在（防止定时器被清除后仍有回调执行）
    if (!this.data.timer) {
      console.log("定时器已被清除，停止距离监听");
      return;
    }

    //检查是否已达到最大执行次数 调试使用
    // if (this.data.handle4F4ECount >= this.data.maxHandle4F4ECount) {
    //   console.log("handle4F4EData已执行6次，停止执行", this.data.commandSource);
    //   return;
    // }

    // 防抖处理，避免频繁触发
    if (this.data.mytypes || this.data.types) {
      return;
    }

    // 如果用户操作正在进行中，延迟执行距离监听
    if (this.data.userOperationPending) {
      console.log("用户操作进行中，延迟距离监听");
      setTimeout(() => {
        this.handle4F4EData();
      }, 1000);
      return;
    }

    // 保存用户当前设置的档位（在设置mytypes之前保存，确保数据一致性）
    const userSetGear = this.data.silderValue1;
    const currentTimestamp = Date.now();

    // 增加计数器和监听状态
    this.setData({
      mytypes: true,
      handle4F4ECount: this.data.handle4F4ECount + 1,
      commandSource: "CMing", // 标记为距离监听来源
      lastDistanceCheckTime: currentTimestamp, // 记录开始时间
    });
    console.log(
      `handle4F4EData第${this.data.handle4F4ECount}次执行，用户档位: ${userSetGear}`
    );

    // 临时设置为15档进行距离监听
    if (userSetGear !== 15) {
      try {
        let results = decimalToHexadecimal(15);
        let data = "FFCC0100" + results + "BBAA";
        await sendData(data);
      } catch (error) {
        console.error("设置15档失败:", error);
        // 如果设置失败，立即停止监听
        this.setData({
          mytypes: false,
          commandSource: "idle",
        });
        return;
      }
    }
    setTimeout(async () => {
      // 再次检查页面状态和时间戳，确保这是当前有效的监听任务
      if (
        this.data.isPageExiting ||
        this.data.commandSource === "exiting" ||
        this.data.lastDistanceCheckTime !== currentTimestamp
      ) {
        console.log("页面正在退出或任务已过期，跳过距离监听回调处理");
        // 清理状态
        this.setData({
          mytypes: false,
          myarrlist: [],
        });
        return;
      }

      // 处理距离数据
      this.setData({
        mytypes: false,
      });
      console.log("实时距离数组：", this.data.myarrlist);

      if (this.data.myarrlist.length > 0) {
        this.cmMinMax(this.data.myarrlist);
        this.checkjl();
        this.setData({
          myarrlist: [],
        });
      } else {
        // toast.info("错误");
        this.setData({
          myarrlist: [],
        });
      }

      // 恢复用户设置的档位，增加更严格的检查
      if (
        userSetGear !== 15 &&
        this.data.hasInitDistance &&
        !this.data.isPageExiting &&
        this.data.commandSource !== "exiting" &&
        this.data.lastDistanceCheckTime === currentTimestamp
      ) {
        this.setData({
          commandSource: "CMing",
        });
        try {
          let distance = decimalToHexadecimal(userSetGear);
          let data = "FFCC0100" + distance + "BBAA";
          await sendData(data);
        } catch (error) {
          console.error("恢复档位失败:", error);
        } finally {
          // 重置指令来源，但要再次检查状态
          setTimeout(() => {
            if (
              !this.data.isPageExiting &&
              this.data.commandSource !== "exiting"
            ) {
              this.setData({ commandSource: "idle" });
            }
          }, 500);
        }
      } else {
        // 如果不需要恢复档位，直接重置指令来源
        if (!this.data.isPageExiting && this.data.commandSource !== "exiting") {
          this.setData({ commandSource: "idle" });
        }
      }

      // 只有不在智能调距状态才隐藏加载
      if (!this.data.types) {
        wx.hideLoading();
      }
    }, 2000);
  },
  cmMinMax(arr: number[]) {
    // 过滤掉NaN和无效值
    const validArr = arr.filter((value) => !isNaN(value) && isFinite(value));
    if (validArr.length === 0) {
      console.warn("数组中没有有效数据，无法计算平均值");
      return;
    }

    // 对数组进行排序
    validArr.sort((a, b) => a - b);
    let newArr = [];
    if (validArr.length < 3) {
      newArr = validArr;
    } else {
      // 去掉一个最大值和一个最小值
      newArr = validArr.slice(1, validArr.length - 1);
    }
    // 计算剩余元素的总和
    let sum = newArr.reduce((a, b) => a + b, 0);
    // 计算平均值
    let average = sum / newArr.length;
    // console.log("平均值：", average);
    let cmNumber = Number((average / 100).toFixed(2));
    // console.log("666", cmNumber);
    this.setData({
      distanceCM: cmNumber,
      // max15cm: cmNumber,
    });
    this.getBottomCm();
  },
  LisentResponseFn() {
    var that = this;
    var LisentResponse: BleDataCallback = {
      async onReceiveData(res) {
        var _data = ab2hex(res.value);
        let data = _data.toUpperCase(); //将数据-转换-成大写
        var res1: any = await AnalyzeData.checkData(data); //这里会返回对象 -成功或失败 及指令内容
        console.log("设备参数：", data);
        if (res1.status == "ok") {
          //数据上报处理
          if (res1.lbstatus == 1) {
            if (that.data.types) {
              let newArrlist = that.data.arrlist;
              const lblongValue = parseInt(res1.lblong);
              if (!isNaN(lblongValue) && isFinite(lblongValue)) {
                newArrlist.push(lblongValue);
              } else {
                console.warn(
                  "检测到无效数据，跳过添加:",
                  res1.lblong,
                  "解析结果:",
                  lblongValue
                );
              }

              that.setData({
                arrlist: newArrlist,
              });
            }
            if (that.data.mytypes) {
              let newArrlist = that.data.myarrlist;
              const lblongValue = parseInt(res1.lblong);
              if (!isNaN(lblongValue) && isFinite(lblongValue)) {
                newArrlist.push(lblongValue);
              } else {
                console.warn(
                  "检测到无效数据，跳过添加:",
                  res1.lblong,
                  "解析结果:",
                  lblongValue
                );
              }

              that.setData({
                myarrlist: newArrlist,
              });
            }
          }
        } else {
          //下发指令接收数据
          console.log("下发指令接收数据", res1.str);
          if (res1.str.slice(4, 6) == "07") {
            const currentGearFromDevice = hexToDec(res1.str.slice(8, 10));
            console.log(
              "设备返回档位:",
              currentGearFromDevice,
              "指令来源:",
              that.data.commandSource
            );

            //读取感应距离 - 增强状态判断逻辑
            // 1. 如果是页面退出状态，忽略所有档位更新
            if (
              that.data.isPageExiting ||
              that.data.commandSource === "exiting"
            ) {
              console.log("页面正在退出，忽略档位读取");
              return;
            }
            // 2. 如果是距离监听状态下的15档，且用户实际档位不是15，则忽略
            if (
              that.data.commandSource === "CMing" &&
              currentGearFromDevice === 15 &&
              that.data.silderValue1 !== 15 &&
              that.data.hasInitDistance
            ) {
              console.log(
                "距离监听状态下的15档响应，忽略更新。用户档位:",
                that.data.silderValue1
              );
              return;
            }

            // 2.5. 特别保护：如果用户操作正在进行中，且设备返回的不是用户目标档位，则忽略
            if (
              that.data.userOperationPending &&
              that.data.userTargetGear > 0 &&
              currentGearFromDevice !== that.data.userTargetGear
            ) {
              console.log(
                "用户操作进行中，设备返回档位",
                currentGearFromDevice,
                "不匹配用户目标档位",
                that.data.userTargetGear,
                "，忽略更新"
              );
              return;
            }

            // 3. 只在以下情况更新用户档位设置：
            // - 未初始化过档位
            // - 明确的用户操作（user）且档位匹配
            // - 初始化查询（init）
            // - 距离监听完成后的恢复操作（CMing且档位与用户设置一致）
            const shouldUpdateUserGear =
              !that.data.hasInitDistance ||
              (that.data.commandSource === "user" &&
                (!that.data.userTargetGear ||
                  currentGearFromDevice === that.data.userTargetGear)) ||
              that.data.commandSource === "init" ||
              (that.data.commandSource === "CMing" &&
                currentGearFromDevice === that.data.silderValue1);

            if (shouldUpdateUserGear) {
              that.setData({
                silderValue1: currentGearFromDevice,
                hasInitDistance: true,
              });
              console.log(
                "更新用户档位设置:",
                currentGearFromDevice,
                "来源:",
                that.data.commandSource
              );
            } else {
              console.log(
                "保持用户档位不变:",
                that.data.silderValue1,
                "设备档位:",
                currentGearFromDevice,
                "来源:",
                that.data.commandSource
              );
            }

            // 只有在非距离监听状态时才重置来源，避免影响后续的设置指令响应
            if (that.data.commandSource !== "CMing") {
              that.setData({ commandSource: "idle" });
            }
          } else if (res1.str.slice(4, 6) == "08") {
            //读取无人持续时间
            var silderValue2 = hexToDec(res1.str.slice(8, 10));
            that.setData({
              silderValue2: silderValue2,
            });
            // console.log("读取无人持续时间", that.data.silderValue2);
          } else if (res1.str.slice(4, 6) == "0A") {
            //8.读取自动开门使能状态
            var checked = res1.str.slice(8, 10) == "01" ? true : false;
            that.setData({
              checked: checked,
            });
            //手动模式
            if (!checked) {
              // console.log("手动模式 :>> ", checked);
              wx.redirectTo({ url: "/pages/pageLB1001hand/pageLB1001hand" });
            }
          } else if (res1.str.slice(4, 6) == "0D") {
            //8.读取版本  FFBB0D000104BBAA
            var Version = res1.str.slice(8, 12);
            var version1 =
              parseInt(Version.slice(0, 2), 10) +
              "." +
              parseInt(Version.slice(2, 4), 10);
            wx.setStorageSync("Version", version1.toString());
            that.setData({
              Version: version1.toString(),
            });
          } else if (res1.str.slice(4, 6) == "0F") {
            //8.读取当前开关门状态
            var openvalue = res1.str.slice(8, 10) == "01" ? "开门" : "关门";
            if (openvalue === "开门") {
              that.setData({
                openDoor_left: "left:-24.5%;",
                openDoor_right: "left:24.5%;",
                openValue: "开门",
              });
            } else {
              that.setData({
                openDoor_left: "left:0;",
                openDoor_right: "left:0;",
                openValue: "关门",
              });
            }
          } else if (res1.str == "FFBB0106BBAA") {
            //FFBB0106BBAA
            // that.openToast(1,'设置成功')
            toast.info("开门成功");
          } else if (res1.str.slice(4, 6) == "01") {
            // that.openToast(1,'设置成功')
            // console.log(
            //   "设置指令响应",
            //   res1.str,
            //   "当前来源:",
            //   that.data.commandSource
            // );

            // 只有用户操作才显示成功提示，距离监听和初始化操作都不显示
            // 增强判断：检查 commandSource 或 userOperationPending 标记
            if (
              that.data.commandSource === "user" ||
              that.data.userOperationPending
            ) {
              //123
              toast.info("设置成功");
            } else {
              console.log(
                "非用户操作，不显示成功提示，来源:",
                that.data.commandSource,
                "用户操作标记:",
                that.data.userOperationPending
              );
            }

            // 指令成功后重置来源和用户操作标记
            that.setData({
              commandSource: "idle",
              userOperationPending: false,
              userTargetGear: 0,
              userOperationTimestamp: 0,
            });
          } else if (res1.str.slice(4, 6) == "00") {
            //下发指令失败
            // that.openToast(2,'设置失败')
            toast.info("设置失败");
            // 指令失败后也重置来源和用户操作标记
            that.setData({
              commandSource: "idle",
              userOperationPending: false,
              userTargetGear: 0,
              userOperationTimestamp: 0,
            });
          } else if (res1.str.slice(4, 6) == "0B") {
            //设置密码
            console.log("设置密码");
            if (res1.str.slice(6, 8) == "01") {
              that.setData({
                isShowPwd: false,
              });
              toast.info("设置成功");
            } else {
              wx.showToast({
                title: "设置失败",
                icon: "none",
                duration: 2000,
              });
            }
          } else if (res1.str.slice(4, 6) == "0C") {
            //校验密码
            // console.log("校验密码", res1.str.slice(6, 8));
            if (res1.str.slice(6, 8) == "01") {
              wx.showToast({
                title: "校验成功",
                icon: "none",
                duration: 2000,
              });
              that.setData({
                isValidation: false,
              });
            } else {
              wx.showToast({
                title: "校验失败",
                icon: "none",
                duration: 2000,
              });
              that.setData({
                isValidation: true,
              });
            }
          } else if (res1.str.slice(4, 6) == "0E") {
            //设置手动关门
            if (res1.str.slice(6, 8) == "00") {
              // that.openToast(1,'设置成功')
              toast.info("关门成功");
              that.setData({
                openValue: "关门",
              });
            } else {
              toast.info("设置失败");
              // that.openToast(2,'设置失败')
            }
          }
        }
      },
    };
    return LisentResponse;
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    sBluetoothManager = app.globalData.bluetoothManager;
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    console.log("页面显示 onShow");

    // 重置页面状态，清理之前的监听状态
    this.setData({
      isPageExiting: false,
      mytypes: false,
      types: false,
      myarrlist: [],
      arrlist: [],
      commandSource: "idle",
      lastDistanceCheckTime: 0,
    });
    console.log("页面状态已重置");

    wx.setStorageSync("isShow", true);
    const isFastPwd = wx.getStorageSync("isFastPwd");
    if (isFastPwd && !wx.getStorageSync("hasShownPwdTip")) {
      wx.showModal({
        title: "设置/修改密码",
        content:
          "请修改蓝牙密码，默认密码下任何人都可以使用小程序手动开关门和设置参数。修改密码后，只有密码正确才能修改参数和手动开关",
        showCancel: false,
        success: () => {
          wx.setStorageSync("hasShownPwdTip", true);
        },
      });
    }
    //wx.showLoading({title:'读取数据中..',mask:true})
    if (BleDataHandler.callbacks.length < 2) {
      BleDataHandler.addCallbacks(this.LisentResponseFn());
    }
    let device: any = sBluetoothManager.getConnectedDevice();
    console.log("连接状态：", device);
    if (device === null) {
      //如果
      return await this.reConnectionFn(1);
    }
    wx.setStorageSync("Version", this.Version);
    const res = await initBLTChange();
    console.log("初始化结果：", res);
    await this.checkPwd();
    wx.hideLoading();
    if (
      getCurrentPages()[getCurrentPages().length - 1].route.match(
        /pageLB1001/
      ) != null
    ) {
      this.setData({ isStudy: false });
      wx.setStorageSync("showToast", true);
    }
  },
  startTimer() {
    // 如果页面正在退出，不启动定时器
    if (this.data.isPageExiting) {
      console.log("页面正在退出，不启动定时器");
      return;
    }

    // 清除之前的定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }

    // 每秒调用一次
    const timer = setInterval(() => {
      this.handle4F4EData();
    }, 2000);
    this.setData({ timer });
    console.log("定时器已启动");
  },
  //恢复用户档位的函数
  async restoreUserGear() {
    const userSetGear = this.data.silderValue1;
    if (userSetGear !== 15 && this.data.hasInitDistance) {
      try {
        this.setData({
          commandSource: "CMing",
        });
        let distance = decimalToHexadecimal(userSetGear);
        let data = "FFCC0100" + distance + "BBAA";
        await sendData(data);
        console.log("页面退出时恢复用户档位:", userSetGear);
      } catch (error) {
        console.error("恢复档位失败:", error);
      }
    }
  },
  // 统一的关停函数
  async oFFcm() {
    console.log("执行页面关停函数 oFFcm");

    // 首先设置页面退出状态，阻止新的距离监听和定时器启动
    this.setData({
      mytypes: false, // 停止当前的距离监听
      types: false, // 停止智能调距
      commandSource: "exiting", // 标记为退出状态
      isPageExiting: true,
    });

    // 立即清除定时器，防止新的handle4F4EData执行
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
      console.log("定时器已清除");
    }

    // 等待当前正在执行的handle4F4EData完成，最多等待3秒
    let waitCount = 0;
    const maxWaitCount = 15; // 3秒 (15 * 200ms)

    while (this.data.mytypes && waitCount < maxWaitCount) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      waitCount++;
      console.log(`等待距离监听完成... ${waitCount}/${maxWaitCount}`);
    }

    // 强制停止所有监听状态
    this.setData({
      mytypes: false,
      types: false,
      myarrlist: [],
      arrlist: [],
    });

    // 恢复用户档位设置
    try {
      await this.restoreUserGear();
      console.log("用户档位已恢复");
    } catch (error) {
      console.error("恢复用户档位失败:", error);
    }

    // 清理UI状态
    wx.hideLoading();
    console.log("页面关停完成");
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  async onHide() {
    this.setData({ isStudy: true });
    wx.setStorageSync("showToast", false);
    console.log("小程序从前台进入后台时触发");
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log("移除监听");
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1]);
    }
    await this.oFFcm();
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  async onUnload() {
    this.setData({ isStudy: true });
    wx.setStorageSync("showToast", false);
    console.log("跳转了");
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log("移除监听");
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1]);
    }
    await this.oFFcm();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
