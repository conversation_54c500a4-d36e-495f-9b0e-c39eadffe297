{"projectname": "HLKOTA", "setting": {"compileHotReLoad": true, "urlCheck": false}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "libVersion": "3.3.5", "condition": {"miniprogram": {"list": [{"name": "pages/pageSetting/pageSetting", "pathName": "pages/pageSetting/pageSetting", "query": "", "scene": null, "launchMode": "default"}, {"name": "pages/LB1001Connect/LB1001Connect", "pathName": "pages/LB1001Connect/LB1001Connect", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/LB1001Update/LB1001Update", "pathName": "pages/LB1001Update/LB1001Update", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/pageLB1001/pageLB1001", "pathName": "pages/pageLB1001/pageLB1001", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/LB1001Firmare/LB1001Firmare", "pathName": "pages/LB1001Firmare/LB1001Firmare", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/pageHome/pageHome", "pathName": "pages/pageHome/pageHome", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/pageDownload/pageDownload", "pathName": "pages/pageDownload/pageDownload", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/pageUpdate/pageUpdate", "pathName": "pages/pageUpdate/pageUpdate", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/pageFirmware/pageFirmware", "pathName": "pages/pageFirmware/pageFirmware", "query": "", "launchMode": "default", "scene": null, "partialCompile": {"enabled": false, "pages": ["pages/pageSetting/pageSetting", "pages/pageUpdate/pageUpdate"]}}, {"name": "", "pathName": "pages/pageConnect/pageConnect", "query": "", "launchMode": "default", "scene": null, "partialCompile": {"enabled": false, "pages": ["pages/pageUpdate/pageUpdate"]}}]}}}